# Axie Studio

A Python package with a built-in web application for AI/ML workflow automation.

## Features

- **80+ Components**: All major AI/ML components
- **Flow Builder**: Visual workflow creation
- **API Integration**: RESTful API endpoints
- **User Management**: Authentication & authorization
- **Real-time Execution**: Live flow monitoring

## Quick Start

```bash
# Install
pip install axiestudio-base

# Run
axiestudio run
```

## Development

### Quick Start (Recommended)

```bash
# Start both backend and frontend in development mode
./dev-start.bat

# Or start them separately:

# Backend only (runs on http://localhost:7860)
./dev-backend.bat

# Frontend only (runs on http://localhost:3000)
./dev-frontend.bat
```

### Manual Setup

```bash
# Backend
cd src/backend/base
uv sync --dev
uv run axiestudio run --dev --host localhost --port 7860

# Frontend (in a new terminal)
cd src/frontend
npm install
npm run start
```

### Development URLs
- **Frontend**: http://localhost:3000 (with hot reload)
- **Backend API**: http://localhost:7860 (with auto-reload)
- **Backend Admin**: http://localhost:7860/admin

## License

MIT 